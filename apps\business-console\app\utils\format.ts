function formatIndianNumber(value: number): string {
  // Format numbers with commas in the Indian system
  return value.toLocaleString("en-IN");
}

export function formatWeight(weight: number) {
  // if (weight % 1 === 0) {
  //     return weight.toString();
  // } else {
  //     return weight.toFixed(2);
  // }
  if (weight >= 1000) {
    // Convert to metric tons
    return `${(weight / 1000).toFixed(1)} Ton`;
  } else if (weight >= 1) {
    // Keep in kilograms
    return `${formatIndianNumber(weight)} Kg`;
  } else {
    // Convert to grams (for fractional kg values)
    return `${(weight * 1000).toFixed(2)} g`;
  }
}

export function formatCurrency(currency: number) {
  // if(currency % 1 === 0) {
  //     return currency.toString();
  // }else {
  //     return currency.toFixed(2);
  // }
  if (currency >= 10000000) {
    // Convert to crores
    return `₹ ${(currency / 10000000).toFixed(1)} Cr`;
  } else if (currency >= 100000) {
    // Convert to lakhs
    return `₹ ${(currency / 100000).toFixed(1)} L`;
    // } else if (currency >= 1000) {
    //     // Convert to thousands
    //     return `₹${(currency / 1000).toFixed(2)} K`;
  } else {
    // Rupees with commas
    return `₹ ${formatIndianNumber(currency)}`;
  }
}

export function camelCaseToWords(text: string) {
  return text
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase());
}


export function sortJSONKeys(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(sortJSONKeys)
  } else if (obj !== null && typeof obj === "object") {
    return Object.keys(obj)
      .sort()
      .reduce((acc: any, key) => {
        acc[key] = sortJSONKeys(obj[key])
        return acc
      }, {})
  }
  return obj
}