import { ApiResponse } from "~/types/api/Api";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export interface NotificationConfig {
  id?: number;
  sellerId: number;
  type: string;
  notificationTemplateId?: number;
  waMobileNumbers: string;
  saMobileNumbers: string;
  waEnabled: boolean;
  saEnabled: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateNotificationConfigRequest {
  sellerId: number;
  type: string;
  notificationTemplateId?: number;
  waMobileNumbers: string;
  saMobileNumbers: string;
  waEnabled: boolean;
  saEnabled: boolean;
}

export interface UpdateNotificationConfigRequest extends CreateNotificationConfigRequest {
  id: number;
}

// Get All Seller Notification Configs
export const getSellerNotificationConfigs = async (sellerId: number, request: Request): Promise<ApiResponse<NotificationConfig[]>> => {
  const response = await apiRequest<{ message: string; success: boolean; sellerConfigs: NotificationConfig[] }>(
    `${API_BASE_URL}/api/notification-config/seller/${sellerId}`,
    "GET",
    undefined,
    {},
    true,
    request
  );
  
  // Extract the sellerConfigs from the response
  const notificationConfigs = response?.data?.sellerConfigs || [];
  
  return {
    data: notificationConfigs,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};

// Get Seller Notification Config by Type
export const getSellerNotificationConfigByType = async (sellerId: number, type: string, request: Request): Promise<ApiResponse<NotificationConfig>> => {
  const response = await apiRequest<{ message: string; success: boolean; sellerConfig?: NotificationConfig }>(
    `${API_BASE_URL}/api/notification-config/seller/${sellerId}/type/${type}`,
    "GET",
    undefined,
    {},
    true,
    request
  );
  
  // Extract the sellerConfig from the response
  const notificationConfig = response?.data?.sellerConfig;
  
  return {
    data: notificationConfig,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};

// Create Seller Notification Config
export const createSellerNotificationConfig = async (data: CreateNotificationConfigRequest, request: Request): Promise<ApiResponse<NotificationConfig>> => {
  const response = await apiRequest<{ message: string; success: boolean; sellerConfig?: NotificationConfig }>(
    `${API_BASE_URL}/api/notification-config/seller`,
    "POST",
    data,
    {},
    true,
    request
  );
  
  // Extract the sellerConfig from the response
  const notificationConfig = response?.data?.sellerConfig;
  
  return {
    data: notificationConfig,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};

// Update Seller Notification Config
export const updateSellerNotificationConfig = async (data: UpdateNotificationConfigRequest, request: Request): Promise<ApiResponse<NotificationConfig>> => {
  const response = await apiRequest<{ message: string; success: boolean; sellerConfig?: NotificationConfig }>(
    `${API_BASE_URL}/api/notification-config/seller`,
    "PUT",
    data,
    {},
    true,
    request
  );
  
  // Extract the sellerConfig from the response
  const notificationConfig = response?.data?.sellerConfig;
  
  return {
    data: notificationConfig,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};

// Delete Seller Notification Config
export const deleteSellerNotificationConfig = async (sellerId: number, type: string, request: Request): Promise<ApiResponse<void>> => {
  const response = await apiRequest<{ message: string; success: boolean }>(
    `${API_BASE_URL}/api/notification-config/seller/${sellerId}/type/${type}`,
    "DELETE",
    undefined,
    {},
    true,
    request
  );
  
  return {
    data: undefined,
    success: response?.data?.success || false,
    status: response?.status || 200,
    headers: response?.headers || new Headers()
  };
};
