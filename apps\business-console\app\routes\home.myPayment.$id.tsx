import { LoaderFunction } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { useState } from "react";
import { getPayoutDetails } from "~/services/payments";
import type { PayoutDetails } from "~/types/api/businessConsoleService/Payouts";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Badge } from "~/components/ui/badge";
import { formatCurrency } from "~/utils/format";
import ResponsivePagination from "~/components/ui/responsivePagination";

interface LoaderData {
  payoutDetails: PayoutDetails;
}

export const loader: LoaderFunction = withAuth(async ({ request, params }) => {
  const payoutId = params?.id;
  if (!payoutId) {
    throw new Response("Invalid payout ID", { status: 400 });
  }

  try {
    const response = await getPayoutDetails(Number(payoutId), request);
    return withResponse({
      payoutDetails: response.data
    }, response?.headers);
  } catch (error) {
    console.error("Error in loader:", error);
    throw new Response("Failed to get payout details", { status: 500 });
  }
});

// Utility functions
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getStatusBadge = (status: string) => {
  const statusColors: Record<string, string> = {
    'PAID': 'bg-green-100 text-green-800',
    'PENDING': 'bg-yellow-100 text-yellow-800',
    'FAILED': 'bg-red-100 text-red-800',
    'PROCESSING': 'bg-blue-100 text-blue-800'
  };

  return (
    <Badge className={statusColors[status] || 'bg-gray-100 text-gray-800'}>
      {status}
    </Badge>
  );
};

// Pagination hook
const usePagination = (data: any[], itemsPerPage: number = 10) => {
  const [currentPage, setCurrentPage] = useState(0);
  const totalPages = Math.ceil(data.length / itemsPerPage);

  const paginatedData = data.slice(
    currentPage * itemsPerPage,
    (currentPage + 1) * itemsPerPage
  );

  return {
    currentPage,
    totalPages,
    paginatedData,
    setCurrentPage
  };
};

// Table components
const SupplierItemsTable = ({ data }: { data: PayoutDetails['supplierItemBds'] }) => {
  const { currentPage, totalPages, paginatedData, setCurrentPage } = usePagination(data);

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Item Name</TableHead>
              <TableHead>Unit</TableHead>
              <TableHead>Distributor</TableHead>
              <TableHead>Weight</TableHead>
              <TableHead>SC</TableHead>
              <TableHead>SC Tax</TableHead>
              <TableHead>SC Total</TableHead>
              <TableHead>PC</TableHead>
              <TableHead>PC Tax</TableHead>
              <TableHead>PC Total</TableHead>
              <TableHead>DHC</TableHead>
              <TableHead>DHC Tax</TableHead>
              <TableHead>DHC Total</TableHead>
              <TableHead>Strikeoff Amount</TableHead>
              <TableHead>Discount</TableHead>
              <TableHead>Items Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedData.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.sellerItemName}</TableCell>
                <TableCell>{item.unit}</TableCell>
                <TableCell>{item.distributorName}</TableCell>
                <TableCell className="whitespace-nowrap">{item.totalWeight} kg</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.sc)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.scTax)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.scTotal)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.pc)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.pcTax)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.pcTotal)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.dhc)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.dhcTax)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.dhcTotal)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.itemsStrikeoffAmount)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.itemsDiscount)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.itemsAmount)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {totalPages > 1 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

const DistributorItemsTable = ({ data }: { data: PayoutDetails['distributorItemBds'] }) => {
  const { currentPage, totalPages, paginatedData, setCurrentPage } = usePagination(data);

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Item Name</TableHead>
              <TableHead>Unit</TableHead>
              <TableHead>Supplier</TableHead>
              <TableHead>Weight</TableHead>
              <TableHead>DHC</TableHead>
              <TableHead>DHC Tax</TableHead>
              <TableHead>DHC Total</TableHead>
              <TableHead>Strikeoff Amount</TableHead>
              <TableHead>Discount</TableHead>
              <TableHead>Items Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedData.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.sellerItemName}</TableCell>
                <TableCell>{item.unit}</TableCell>
                <TableCell>{item.supplierName}</TableCell>
                <TableCell className="whitespace-nowrap">{item.totalWeight} kg</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.dhc)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.dhcTax)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.dhcTotal)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.itemsStrikeoffAmount)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.itemsDiscount)}</TableCell>
                <TableCell className="whitespace-nowrap">{formatCurrency(item.itemsAmount)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {totalPages > 1 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

const OrderGroupsTable = ({ data }: { data: PayoutDetails['orderGroupBds'] }) => {
  const { currentPage, totalPages, paginatedData, setCurrentPage } = usePagination(data);

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Order Group ID</TableHead>
              <TableHead>Business Name</TableHead>
              <TableHead>Delivery Date</TableHead>
              <TableHead>Driver</TableHead>
              <TableHead>Agent</TableHead>
              <TableHead>COD Amount</TableHead>
              <TableHead>Credit Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedData.map((item) => (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.ogId}</TableCell>
                <TableCell>{item.businessName}</TableCell>
                <TableCell>{formatDateTime(item.deliveryDate)}</TableCell>
                <TableCell>{item.driverName}</TableCell>
                <TableCell>{item.agentName}</TableCell>
                <TableCell>{formatCurrency(item.codAmount)}</TableCell>
                <TableCell>{formatCurrency(item.creditAmount)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {totalPages > 1 && (
        <ResponsivePagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};

export default function PayoutDetails() {
  const { payoutDetails } = useLoaderData<LoaderData>();

  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Payout Details</h1>
        <Badge variant="outline" className="text-lg px-3 py-1">
          ID: {payoutDetails?.sellerPayout?.payoutId}
        </Badge>
      </div>

      {/* Seller Payout Details */}
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Payout Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Date Range</p>
              <p className="text-lg font-semibold">{payoutDetails?.sellerPayout?.dateRange}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Payment Date</p>
              <p className="text-lg font-semibold">{formatDate(payoutDetails?.sellerPayout?.paymentDate)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Amount</p>
              <p className="text-lg font-semibold text-green-600">{formatCurrency(payoutDetails?.sellerPayout?.amount)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Status</p>
              <div>{getStatusBadge(payoutDetails?.sellerPayout?.status)}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Supplier Items Section */}
      {payoutDetails?.supplierItemBds?.length > 0 && (<Card>
        <CardHeader>
          <CardTitle className="text-xl">
            Supplier Items ({payoutDetails?.supplierItemBds?.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {payoutDetails?.supplierItemBds?.length > 0 ? (
            <SupplierItemsTable data={payoutDetails?.supplierItemBds} />
          ) : (
            <p className="text-center text-muted-foreground py-8">No supplier items found</p>
          )}
        </CardContent>
      </Card>)}

      {/* Distributor Items Section */}
      { payoutDetails?.distributorItemBds?.length > 0 && (<Card>
        <CardHeader>
          <CardTitle className="text-xl">
            Distributor Items ({payoutDetails?.distributorItemBds?.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {payoutDetails?.distributorItemBds?.length > 0 ? (
            <DistributorItemsTable data={payoutDetails?.distributorItemBds} />
          ) : (
            <p className="text-center text-muted-foreground py-8">No distributor items found</p>
          )}
        </CardContent>
      </Card>)}

      {/* Order Groups Section */}
      {payoutDetails?.orderGroupBds?.length > 0 && (<Card>
        <CardHeader>
          <CardTitle className="text-xl">
            Order Groups ({payoutDetails?.orderGroupBds?.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {payoutDetails?.orderGroupBds?.length > 0 ? (
            <OrderGroupsTable data={payoutDetails?.orderGroupBds} />
          ) : (
            <p className="text-center text-muted-foreground py-8">No order groups found</p>
          )}
        </CardContent>
      </Card>)}
    </div>
  );
};