import { API_BASE_URL, apiRequest } from "@utils/api";
import { Transaction } from "~/types/api/businessConsoleService/payments";
import { Payout, PayoutDetails, WalletHistory, WalletInfo } from "~/types/api/businessConsoleService/Payouts";
import { ApiResponse } from "~/types/api/Api";

export async function getTransactionDetails(
  date: string,
  request: Request
): Promise<ApiResponse<Transaction[]>> {
  const response = await apiRequest<Transaction[]>(
    `${API_BASE_URL}/mc/deposits/${date}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch buyer summary data");
  }
}

export async function updateTransaction(
  depositId: number | null,
  request: Request
): Promise<ApiResponse<Transaction>> {
  return apiRequest<Transaction>(
    `${API_BASE_URL}/mc/deposit/${depositId}/updatestatus`,
    "PUT", // Fixed capitalization for consistency
    undefined,
    {},
    true,
    request
  );
}

export async function updateMarkAsPaid(
  userId: number,
  depositId: number,
  request: Request
): Promise<ApiResponse<Transaction[]>> {
  return apiRequest<Transaction[]>(
    `${API_BASE_URL}/bc/user/${userId}/deposit/${depositId}/manualstatusupdate`,
    "PUT", // Fixed capitalization for consistency
    undefined,
    {},
    true,
    request
  );
}

export async function getWalletInfo(
  request: Request
): Promise<ApiResponse<WalletInfo>> {
  const response = await apiRequest<WalletInfo>(
    `${API_BASE_URL}/bc/seller/wallet`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch wallet information");
  }
}

export async function getPayoutList(
  pageNo: number,
  pageSize: number,
  request: Request
): Promise<ApiResponse<Payout[]>> {
  const response = await apiRequest<Payout[]>(
    `${API_BASE_URL}/bc/seller/payouts?pageNo=${pageNo}&size=${pageSize}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch payout list");
  }
}

export async function getPayoutDetails(
  payoutId: number,
  request: Request
): Promise<ApiResponse<PayoutDetails>> {
  const response = await apiRequest<PayoutDetails>(
    `${API_BASE_URL}/bc/seller/payout/${payoutId}`,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch payout details");
  }
}

export async function initiateWithdraw(
  amount: number,
  request: Request
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  const response = await apiRequest<{ success: boolean; message: string }>(
    `${API_BASE_URL}/bc/seller/withdraw`,
    "POST",
    { amount },
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to initiate withdrawal");
  }
}

export async function getWalletHistory(
  request: Request,
  pageNo: number,
  pageSize: number,
  fromDate?: string,
  toDate?: string
): Promise<ApiResponse<{walletEntries : WalletHistory[], totalElements: number, totalPages: number, currentPage: number , pageSize: number, hasNext: boolean, hasPrevious: boolean }>> {
  const url = fromDate && toDate ? `${API_BASE_URL}/bc/seller/wallet/history?fromDate=${fromDate}&toDate=${toDate}&pageNo=${pageNo}&size=${pageSize}` : `${API_BASE_URL}/bc/seller/wallet/history?pageNo=${pageNo}&size=${pageSize}`;
  const response = await apiRequest<{walletEntries : WalletHistory[], totalElements: number, totalPages: number, currentPage: number , pageSize: number, hasNext: boolean, hasPrevious: boolean }>(
    url,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (response) {
    return response;
  } else {
    throw new Error("Failed to fetch wallet history");
  }
}
