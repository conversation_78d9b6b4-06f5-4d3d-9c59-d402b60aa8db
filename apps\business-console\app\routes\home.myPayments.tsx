import { LoaderFunction, ActionFunction } from "@remix-run/node";
import { useLoaderData, useActionData, Form, useNavigation, useNavigate } from "@remix-run/react";
import { Payout, WalletInfo } from "~/types/api/businessConsoleService/Payouts";
import { getWalletInfo, getPayoutList, initiateWithdraw } from "~/services/payments";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Wallet, Download, Calendar, DollarSign, TrendingUp, ChevronLeft, ChevronRight } from "lucide-react";
import { Alert, AlertDescription } from "~/components/ui/alert";
import SpinnerLoader from "~/components/loader/SpinnerLoader";

interface LoaderData {
    walletInfo: WalletInfo;
    payouts: Payout[];
    pageNo: number;
    pageSize: number;
}

interface ActionData {
    success?: boolean;
    error?: string;
    message?: string;
}

export const loader: LoaderFunction = withAuth(async ({ request }) => {
    const url = new URL(request.url);
    const pageNo = Number(url.searchParams.get("pageNo")) || 0;
    const pageSize = Number(url.searchParams.get("pageSize")) || 10;
    try {
        const [walletResponse, payoutsResponse] = await Promise.all([
            getWalletInfo(request),
            getPayoutList(pageNo, pageSize, request)
        ]);

        return withResponse({
            walletInfo: walletResponse.data,
            payouts: payoutsResponse.data,
            pageNo,
            pageSize
        }, walletResponse.headers);
    } catch (error) {
        console.error("Payments loader error:", error);

        return withResponse({
            walletInfo: null,
            payouts: [],
            pageNo,
            pageSize
        }, new Headers());
    }
});

export const action: ActionFunction = withAuth(async ({ request }) => {
    const formData = await request.formData();
    const actionType = formData.get("_action");

    if (actionType === "withdraw") {
        try {
            const amount = Number(formData.get("amount"));
            if (!amount || amount <= 0) {
                return { success: false, error: "Invalid withdrawal amount" };
            }

            const response = await initiateWithdraw(amount, request);
            return {
                success: response.data?.success || false,
                message: response.data?.message || "Withdrawal initiated successfully"
            };
        } catch (error) {
            console.error("Withdrawal error:", error);
            return { success: false, error: "Failed to initiate withdrawal" };
        }
    }

    return { success: false, error: "Invalid action" };
});

export default function MyPayments() {
    const { walletInfo, payouts, pageNo, pageSize } = useLoaderData<LoaderData>();
    const actionData = useActionData<ActionData>();
    const navigation = useNavigation();
    const isSubmitting = navigation.state === "submitting";
    const navigate = useNavigate();

    const getStatusBadge = (status: string) => {
        const statusLower = status.toLowerCase();
        if (statusLower === "completed") {
            return <Badge variant="default" className="bg-green-100 text-green-800 text-xs sm:text-sm whitespace-nowrap">Completed</Badge>;
        } else if (statusLower === "pending") {
            return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-xs sm:text-sm whitespace-nowrap">Pending</Badge>;
        } else if (statusLower === "failed") {
            return <Badge variant="destructive" className="text-xs sm:text-sm whitespace-nowrap">Failed</Badge>;
        } else if (statusLower === "in review") {
            return <Badge variant="outline" className="bg-blue-100 text-blue-800 text-xs sm:text-sm whitespace-nowrap">In Review</Badge>;
        }
        return <Badge variant="outline" className="text-xs sm:text-sm whitespace-nowrap break-all">{status}</Badge>;
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-IN', {
            style: 'currency',
            currency: 'INR'
        }).format(amount);
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    if (!walletInfo) {
        return <SpinnerLoader loading={true} />;
    }

    return (
        <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-7xl">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                <div>
                    <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 break-words">My Payments</h1>
                    <p className="text-sm sm:text-base text-gray-600 mt-1">Manage your payments and view payout history</p>
                </div>
            </div>

            {actionData?.success && (
                <Alert className="border-green-200 bg-green-50">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                        {actionData.message}
                    </AlertDescription>
                </Alert>
            )}

            {actionData?.error && (
                <Alert className="border-red-200 bg-red-50">
                    <AlertDescription className="text-red-800">
                        {actionData.error}
                    </AlertDescription>
                </Alert>
            )}

            {/* Wallet Information Card */}
            <Card className="border-0 shadow-md">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg p-2 sm:p-4">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                        <div className="flex items-center space-x-3">
                            <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                                <Wallet className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                            </div>
                            <div className="min-w-0 flex-1">
                                <CardTitle className="text-lg sm:text-xl text-gray-900 break-words">Wallet Balance</CardTitle>
                                <CardDescription className="text-sm sm:text-base text-gray-600 break-words">
                                    Available funds for withdrawal
                                </CardDescription>
                            </div>
                        </div>
                        <div className="text-left sm:text-right">
                            <div className="text-2xl sm:text-3xl font-bold text-gray-900 break-all">
                                {formatCurrency(walletInfo.walletBalance)}
                            </div>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="pt-4 sm:pt-6 p-4 sm:p-6">
                    <Form method="post" className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                        <input type="hidden" name="_action" value="withdraw" />
                        <input
                            type="hidden"
                            name="amount"
                            value={walletInfo.walletBalance}
                        />
                        <Button
                            type="submit"
                            // disabled={!walletInfo.withdrawEnabled || isSubmitting || walletInfo.walletBalance <= 0}
                            disabled={true}
                            className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white px-4 sm:px-6 py-2 rounded-lg flex items-center justify-center space-x-2 text-sm sm:text-base"
                        >
                            <Download className="h-4 w-4 flex-shrink-0" />
                            <span className="truncate">{isSubmitting ? 'Processing...' : 'Withdraw Funds'}</span>
                        </Button>
                    </Form>
                </CardContent>
            </Card>

            {/* Payout History */}
            <Card className="border-0 shadow-md">
                <CardHeader className="p-4 sm:p-6 pb-2">
                    <div className="flex items-center space-x-3">
                        <div className="p-2 bg-green-100 rounded-lg flex-shrink-0">
                            <Calendar className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                            <CardTitle className="text-lg sm:text-xl text-gray-900 break-words">Payout History</CardTitle>
                            <CardDescription className="text-sm sm:text-base text-gray-600 break-words">
                                Track your payment transactions and settlements
                            </CardDescription>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    {payouts && payouts.length > 0 ? (
                        <div className="overflow-x-auto -mx-4 sm:mx-0">
                            <div className="min-w-full inline-block align-middle">
                                <Table className="min-w-full">
                                    <TableHeader>
                                        <TableRow className="bg-gray-50">
                                            <TableHead className="font-semibold text-gray-700 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 whitespace-nowrap">ID</TableHead>
                                            <TableHead className="font-semibold text-gray-700 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 min-w-[120px] sm:min-w-[160px]">Date Range</TableHead>
                                            <TableHead className="font-semibold text-gray-700 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 whitespace-nowrap">Payment Date</TableHead>
                                            <TableHead className="font-semibold text-gray-700 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 text-right whitespace-nowrap">Amount</TableHead>
                                            <TableHead className="font-semibold text-gray-700 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 whitespace-nowrap">Status</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {payouts.map((payout) => (
                                            <TableRow key={payout.payoutId} className="hover:bg-gray-50 transition-colors cursor-pointer" onClick={() => navigate(`/home/<USER>/${payout.payoutId}`)}>
                                                <TableCell className="font-medium text-gray-900 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 whitespace-nowrap">
                                                    {payout.payoutId}
                                                </TableCell>
                                                <TableCell className="text-gray-600 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3">
                                                    <div className="break-words max-w-[120px] sm:max-w-none">
                                                        {payout.dateRange}
                                                    </div>
                                                </TableCell>
                                                <TableCell className="text-gray-600 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 whitespace-nowrap">
                                                    {formatDate(payout.paymentDate)}
                                                </TableCell>
                                                <TableCell className="text-right font-semibold text-gray-900 text-xs sm:text-sm px-2 sm:px-4 py-2 sm:py-3 whitespace-nowrap">
                                                    {formatCurrency(payout.amount)}
                                                </TableCell>
                                                <TableCell className="px-2 sm:px-4 py-2 sm:py-3">
                                                    {getStatusBadge(payout.status)}
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </div>
                        </div>
                    ) : (
                        <div className="text-center py-8 sm:py-12 px-4">
                            <div className="p-3 sm:p-4 bg-gray-100 rounded-full w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-4 flex items-center justify-center">
                                <DollarSign className="h-6 w-6 sm:h-8 sm:w-8 text-gray-400" />
                            </div>
                            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2 break-words">No Payouts Yet</h3>
                            <p className="text-sm sm:text-base text-gray-600 break-words max-w-md mx-auto">
                                Your payout history will appear here once you start receiving payments.
                            </p>
                        </div>
                    )}
                    <div className="flex items-center justify-end space-x-3 mt-6">
                        <div className="p-1.5 border rounded-md text-sm cursor-pointer" onClick={() => navigate(`/home/<USER>/div>
                        <div className="text-sm text-gray-500">
                            Page {pageNo + 1}
                        </div>
                        <div className="p-1.5 border rounded-md cursor-pointer" onClick={() => {
                            if (pageNo > 0) {
                                navigate(`/home/<USER>
                            }
                        }}
                        >
                            <ChevronLeft className="h-5 w-5" />
                        </div>
                        <div className="p-1.5 border rounded-md cursor-pointer" onClick={() => {
                            if (payouts.length === pageSize) {
                                navigate(`/home/<USER>
                            }
                        }}
                        >
                            <ChevronRight className="h-5 w-5" />
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}