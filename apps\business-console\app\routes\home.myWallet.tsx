import { Wallet } from "lucide-react";
import { Card, CardDes<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "~/components/ui/card";

export default function MyWallet() {
  return <div
    className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-7xl">
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 break-words">My Wallet</h1>
        <p className="text-sm sm:text-base text-gray-600 mt-1">Manage your wallet and view wallet history</p>
      </div>
    </div>

    {/* Wallet Information Card */}
    <Card className="border-0 shadow-md">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg p-2 sm:p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
              <Wallet className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
            </div>
            <div className="min-w-0 flex-1">
              <CardTitle className="text-lg sm:text-xl text-gray-900 break-words">Wallet Balance</CardTitle>
              <CardDescription className="text-sm sm:text-base text-gray-600 break-words">
                Available funds for withdrawal
              </CardDescription>
            </div>
          </div>
          <div className="text-left sm:text-right">
            <div className="text-2xl sm:text-3xl font-bold text-gray-900 break-all">
              {formatCurrency(walletInfo.walletBalance)}
            </div>
          </div>
        </div>
      </CardHeader>
    </Card>
  </div>;
}